import React, { useEffect, useRef, useState } from 'react';
import { Link, useLocation } from '@tanstack/react-router';
import { ChevronRightIcon, CheckIcon, EyeIcon, ChevronLeftIcon } from '@heroicons/react/24/outline';
import { useCertificateType, type CertificateType } from '../../hooks/useCertificateType';
import { useNavigationState, type PageType } from '../../hooks/useNavigationState';

// Define the page configuration for each certificate type
const certificateTypePages: Record<CertificateType, PageType[]> = {
  'WG/V': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'verbrauch',
    'zusammenfassung'
  ],
  'WG/B': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'fenster',
    'heizung',
    'tww-lueftung',
    'zusammenfassung'
  ],
  'NWG/V': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'verbrauch',
    'zusammenfassung'
  ]
};

// Page metadata with responsive titles
const pageMetadata: Record<PageType, { title: string; shortTitle: string; path: string }> = {
  'objektdaten': {
    title: 'Objektdaten',
    shortTitle: 'Objekt',
    path: '/erfassen/objektdaten'
  },
  'gebaeudedetails1': {
    title: 'Gebäudedetails 1',
    shortTitle: 'Details 1',
    path: '/erfassen/gebaeudedetails1'
  },
  'gebaeudedetails2': {
    title: 'Gebäudedetails 2',
    shortTitle: 'Details 2',
    path: '/erfassen/gebaeudedetails2'
  },
  'fenster': {
    title: 'Fenster',
    shortTitle: 'Fenster',
    path: '/erfassen/fenster'
  },
  'heizung': {
    title: 'Heizung',
    shortTitle: 'Heizung',
    path: '/erfassen/heizung'
  },
  'tww-lueftung': {
    title: 'TWW & Lüftung',
    shortTitle: 'TWW',
    path: '/erfassen/tww-lueftung'
  },
  'verbrauch': {
    title: 'Verbrauch',
    shortTitle: 'Verbrauch',
    path: '/erfassen/verbrauch'
  },
  'zusammenfassung': {
    title: 'Zusammenfassung',
    shortTitle: 'Übersicht',
    path: '/erfassen/zusammenfassung'
  }
};

interface BreadcrumbProps {
  className?: string;
}

// Responsive breadcrumb item component
const BreadcrumbItem: React.FC<{
  index: number;
  isCurrentPage: boolean;
  isCompleted: boolean;
  isVisited: boolean;
  isAccessible: boolean;
  metadata: { title: string; shortTitle: string; path: string };
  showShortTitle: boolean;
}> = ({
  index,
  isCurrentPage,
  isCompleted,
  isVisited,
  isAccessible,
  metadata,
  showShortTitle
}) => {
  const displayTitle = showShortTitle ? metadata.shortTitle : metadata.title;

  return (
    <>
      {index > 0 && (
        <ChevronRightIcon className="h-3 w-3 sm:h-4 sm:w-4 text-gray-400 flex-shrink-0" />
      )}

      <div className="flex items-center min-w-0">
        {isAccessible ? (
          <Link
            to={metadata.path}
            className={`flex items-center px-2 py-1.5 sm:px-3 sm:py-2 rounded-md transition-colors text-xs sm:text-sm min-w-0 ${
              isCurrentPage
                ? 'bg-blue-100 text-blue-800 font-medium'
                : isCompleted
                ? 'text-green-600 hover:text-green-800 hover:bg-green-50'
                : isVisited
                ? 'text-blue-600 hover:text-blue-800 hover:bg-blue-50'
                : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
            }`}
            title={showShortTitle ? metadata.title : undefined}
          >
            {isCompleted && (
              <CheckIcon className="h-3 w-3 sm:h-4 sm:w-4 mr-1 text-green-500 flex-shrink-0" data-testid="check-icon" />
            )}
            {isVisited && !isCompleted && (
              <EyeIcon className="h-3 w-3 sm:h-4 sm:w-4 mr-1 text-blue-500 flex-shrink-0" data-testid="eye-icon" />
            )}
            <span className="truncate">{displayTitle}</span>
          </Link>
        ) : (
          <span
            className="flex items-center px-2 py-1.5 sm:px-3 sm:py-2 text-gray-400 cursor-not-allowed text-xs sm:text-sm min-w-0"
            title={showShortTitle ? metadata.title : undefined}
          >
            <span className="truncate">{displayTitle}</span>
          </span>
        )}
      </div>
    </>
  );
};

export const Breadcrumb: React.FC<BreadcrumbProps> = ({
  className = ''
}) => {
  const location = useLocation();
  const { certificateType, isLoading } = useCertificateType();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showScrollButtons, setShowScrollButtons] = useState(false);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);

  // Always call hooks in the same order - handle null certificateType inside the hook
  const {
    markPageAsVisited,
    isPageAccessible,
    isPageCompleted,
    isPageVisited
  } = useNavigationState(certificateType);

  // Determine current page from URL
  const currentPath = location.pathname;
  const currentPageType = Object.entries(pageMetadata).find(
    ([_, meta]) => meta.path === currentPath
  )?.[0] as PageType;

  // Always call useEffect in the same position - handle conditions inside
  useEffect(() => {
    if (currentPageType && certificateType) {
      markPageAsVisited(currentPageType);
    }
  }, [currentPageType, markPageAsVisited, certificateType]);

  // Check if scrolling is needed
  useEffect(() => {
    const checkScrollable = () => {
      if (scrollContainerRef.current) {
        const { scrollWidth, clientWidth, scrollLeft } = scrollContainerRef.current;
        const isScrollable = scrollWidth > clientWidth;
        setShowScrollButtons(isScrollable);
        setCanScrollLeft(scrollLeft > 0);
        setCanScrollRight(scrollLeft < scrollWidth - clientWidth);
      }
    };

    checkScrollable();
    window.addEventListener('resize', checkScrollable);
    return () => window.removeEventListener('resize', checkScrollable);
  }, [certificateType]);

  // Handle loading and null states after all hooks are called
  if (isLoading || !certificateType) {
    return null;
  }

  // Get the pages for the current certificate type
  const pages = certificateTypePages[certificateType];

  // Determine if we should show short titles (for WG/B type or small screens)
  const shouldUseShortTitles = certificateType === 'WG/B';

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -200, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 200, behavior: 'smooth' });
    }
  };

  const handleScroll = () => {
    if (scrollContainerRef.current) {
      const { scrollWidth, clientWidth, scrollLeft } = scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth);
    }
  };

  return (
    <nav className={`relative ${className}`} aria-label="Breadcrumb">
      {/* Scroll button left */}
      {showScrollButtons && canScrollLeft && (
        <button
          onClick={scrollLeft}
          className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white shadow-md rounded-full p-1 hover:bg-gray-50 transition-colors"
          aria-label="Nach links scrollen"
        >
          <ChevronLeftIcon className="h-4 w-4 text-gray-600" />
        </button>
      )}

      {/* Breadcrumb container */}
      <div
        ref={scrollContainerRef}
        onScroll={handleScroll}
        className={`flex items-center gap-1 sm:gap-2 text-sm overflow-x-auto scrollbar-hide ${
          showScrollButtons ? 'px-8' : ''
        }`}
        style={{
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
        }}
      >
        {pages.map((pageType, index) => {
          const isCurrentPage = pageType === currentPageType;
          const isCompleted = isPageCompleted(pageType, currentPageType);
          const isVisited = isPageVisited(pageType, currentPageType);
          const isAccessible = isPageAccessible(pageType, currentPageType);
          const metadata = pageMetadata[pageType];

          return (
            <BreadcrumbItem
              key={pageType}
              index={index}
              isCurrentPage={isCurrentPage}
              isCompleted={isCompleted}
              isVisited={isVisited}
              isAccessible={isAccessible}
              metadata={metadata}
              showShortTitle={shouldUseShortTitles}
            />
          );
        })}
      </div>

      {/* Scroll button right */}
      {showScrollButtons && canScrollRight && (
        <button
          onClick={scrollRight}
          className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white shadow-md rounded-full p-1 hover:bg-gray-50 transition-colors"
          aria-label="Nach rechts scrollen"
        >
          <ChevronRightIcon className="h-4 w-4 text-gray-600" />
        </button>
      )}
    </nav>
  );
};
